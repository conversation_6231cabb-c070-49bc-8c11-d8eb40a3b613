'use client'

import { useAuth } from '@/contexts/auth-context'
import { useTenant } from '@/contexts/tenant-context'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import DateDisplacementDebugger from '@/components/debug/DateDisplacementDebugger'
import DateValidationMonitor from '@/components/debug/DateValidationMonitor'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useAuth()
  const { organization } = useTenant()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {children}

      {/* Debug Tools - Only in development or when explicitly enabled */}
      <DateDisplacementDebugger
        enabled={process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_ENABLE_DEBUG === 'true'}
        autoStart={true}
        showUI={true}
      />
      <DateValidationMonitor />
    </div>
  )
}
